#!/usr/bin/env python3
"""
Standalone script to create an Odoo database via POST request to /web/database/create
"""

import requests
import sys
import time
from urllib.parse import urljoin


class OdooDatabaseCreator:
    def __init__(self, base_url="http://localhost:8069", master_password="ttl1034"):
        """
        Initialize the database creator
        
        Args:
            base_url (str): Base URL of the Odoo server
            master_password (str): Master password for database operations
        """
        self.base_url = base_url.rstrip('/')
        self.master_password = master_password
        self.session = requests.Session()
        
    def check_server_status(self):
        """Check if Odoo server is running and accessible"""
        try:
            response = self.session.get(self.base_url, timeout=10)
            if response.status_code == 200:
                print(f"✅ Odoo server is accessible at {self.base_url}")
                return True
            else:
                print(f"❌ Odoo server returned status code: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            print(f"❌ Cannot connect to Odoo server at {self.base_url}")
            print("   Make sure the server is running (try: python odoo_runner.py)")
            return False
        except requests.exceptions.Timeout:
            print(f"❌ Timeout connecting to Odoo server at {self.base_url}")
            return False
        except Exception as e:
            print(f"❌ Error connecting to server: {e}")
            return False
    
    def create_database(self, db_name, admin_login="admin", admin_password="admin", 
                       language="en_US", include_demo=False, country_code=None, phone=""):
        """
        Create a new Odoo database
        
        Args:
            db_name (str): Name of the database to create
            admin_login (str): Admin user login
            admin_password (str): Admin user password
            language (str): Default language (e.g., 'en_US')
            include_demo (bool): Whether to include demo data
            country_code (str): Country code (optional)
            phone (str): Phone number (optional)
            
        Returns:
            bool: True if database was created successfully, False otherwise
        """
        print(f"🚀 Creating database '{db_name}'...")
        
        # First check if server is accessible
        if not self.check_server_status():
            return False
        
        # Prepare the database creation URL
        create_url = urljoin(self.base_url, '/web/database/create')
        
        # Prepare form data
        form_data = {
            'master_pwd': self.master_password,
            'name': db_name,
            'login': admin_login,
            'password': admin_password,
            'lang': language,
            'phone': phone,
            'demo': include_demo,
            'country_code': country_code or False
        }
        
        print(f"📝 Request details:")
        print(f"   URL: {create_url}")
        print(f"   Database name: {db_name}")
        print(f"   Admin login: {admin_login}")
        print(f"   Language: {language}")
        print(f"   Demo data: {include_demo}")
        print(f"   Country code: {country_code}")
        
        try:
            # Send POST request to create database
            print("📤 Sending database creation request...")
            response = self.session.post(
                create_url,
                data=form_data,
                allow_redirects=False,  # Don't follow redirects to see the response
                timeout=120  # Database creation can take time
            )
            
            print(f"📥 Response status: {response.status_code}")
            
            # Check response
            if response.status_code == 303:
                # Successful creation typically returns a redirect (303)
                redirect_location = response.headers.get('Location', '')
                print(f"✅ Database '{db_name}' created successfully!")
                print(f"   Redirect location: {redirect_location}")
                
                if '/odoo' in redirect_location:
                    print(f"🌐 You can access the database at: {self.base_url}/odoo?db={db_name}")
                    print(f"🔑 Login credentials: {admin_login} / {admin_password}")
                
                return True
                
            elif response.status_code == 200:
                # If we get 200, it might be an error page
                if 'error' in response.text.lower() or 'exception' in response.text.lower():
                    print(f"❌ Database creation failed (returned error page)")
                    # Try to extract error message
                    if 'Database creation error:' in response.text:
                        import re
                        error_match = re.search(r'Database creation error: ([^<]+)', response.text)
                        if error_match:
                            print(f"   Error: {error_match.group(1).strip()}")
                    return False
                else:
                    print(f"⚠️  Unexpected 200 response - database creation status unclear")
                    return False
                    
            else:
                print(f"❌ Database creation failed with status code: {response.status_code}")
                if response.text:
                    print(f"   Response: {response.text[:500]}...")
                return False
                
        except requests.exceptions.Timeout:
            print("❌ Request timed out - database creation may still be in progress")
            print("   Check the Odoo server logs for more information")
            return False
        except requests.exceptions.ConnectionError:
            print("❌ Connection error during database creation")
            return False
        except Exception as e:
            print(f"❌ Unexpected error during database creation: {e}")
            return False
    
    def list_databases(self):
        """List existing databases (if accessible)"""
        try:
            list_url = urljoin(self.base_url, '/web/database/list')
            response = self.session.post(
                list_url,
                json={},
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                try:
                    databases = response.json()
                    if isinstance(databases, dict) and 'result' in databases:
                        databases = databases['result']
                    print(f"📋 Existing databases: {databases}")
                    return databases
                except:
                    print("⚠️  Could not parse database list response")
                    return []
            else:
                print(f"⚠️  Could not retrieve database list (status: {response.status_code})")
                return []
        except Exception as e:
            print(f"⚠️  Error retrieving database list: {e}")
            return []


def main():
    """Main function to create a database"""
    print("🎯 Odoo Database Creator")
    print("=" * 50)
    
    # Initialize creator with default settings
    creator = OdooDatabaseCreator()
    
    # Get database name from command line or use default
    if len(sys.argv) > 1:
        db_name = sys.argv[1]
    else:
        db_name = input("Enter database name (or press Enter for 'test_db'): ").strip()
        if not db_name:
            db_name = "test_db"
    
    # Validate database name
    import re
    if not re.match(r'^[a-zA-Z0-9_.-]+$', db_name):
        print("❌ Invalid database name. Use only letters, numbers, underscores, hyphens, or dots.")
        sys.exit(1)
    
    print(f"\n📊 Configuration:")
    print(f"   Server URL: {creator.base_url}")
    print(f"   Master password: {'*' * len(creator.master_password)}")
    print(f"   Database name: {db_name}")
    
    # List existing databases first
    print(f"\n📋 Checking existing databases...")
    creator.list_databases()
    
    # Create the database
    print(f"\n🚀 Starting database creation...")
    success = creator.create_database(
        db_name=db_name,
        admin_login="admin",
        admin_password="admin",
        language="en_US",
        include_demo=False,  # Set to True if you want demo data
        country_code=None,
        phone=""
    )
    
    if success:
        print(f"\n🎉 Database creation completed successfully!")
        print(f"   Database: {db_name}")
        print(f"   URL: {creator.base_url}/odoo?db={db_name}")
        print(f"   Login: admin / admin")
        sys.exit(0)
    else:
        print(f"\n💥 Database creation failed!")
        print(f"   Check the Odoo server logs for more details")
        sys.exit(1)


if __name__ == "__main__":
    main()
